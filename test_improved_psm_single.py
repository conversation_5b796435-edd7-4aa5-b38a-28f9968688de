#!/usr/bin/env python3
"""
测试改进版PSM代码的单个limit处理，用于验证修复
"""

import sys
import os
import time
from datetime import datetime

# 直接导入改进版本的函数
sys.path.insert(0, 'src_new_new')
try:
    from PSM_matching_multi_limits_improved import process_single_limit
    print("✅ Successfully imported process_single_limit")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Trying alternative import...")
    # 如果直接导入失败，尝试执行文件
    exec(open('src_new_new/20250629_04_PSM_matching_multi_limits_improved.py').read())

def test_single_limit():
    """测试单个limit的处理"""
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试limit 180（数据量相对较小）
    test_limit = 180
    
    print(f"Testing limit {test_limit}...")
    start_time = time.time()
    
    try:
        result = process_single_limit(test_limit)
        end_time = time.time()
        
        if result:
            limit, processing_time, peak_memory = result
            print(f"\n=== TEST RESULTS ===")
            print(f"Limit: {limit}")
            print(f"Processing time: {processing_time:.2f}s")
            print(f"Peak memory: {peak_memory:.1f}MB")
            print(f"Total test time: {end_time - start_time:.2f}s")
            print("===================")
            return True
        else:
            print("❌ Function returned None or empty result")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    success = test_single_limit()
    sys.exit(0 if success else 1)
