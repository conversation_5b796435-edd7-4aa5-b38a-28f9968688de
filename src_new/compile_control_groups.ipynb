import pandas as pd
import os
import logging
import numpy as np
from sklearn.neighbors import NearestNeighbors
import statsmodels.api as sm
import matplotlib.pyplot as plt
log_dir = "../logs"
os.makedirs(log_dir, exist_ok=True)
# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "psm_2025_0227.log")),
        logging.StreamHandler(),
    ],
)

output_dir = "../result/did_result_20250227/"
os.makedirs(output_dir, exist_ok=True)


near_days = 7
burst_gap = 84
window_size = 12
ratio_control_num = 5

def save_data(data, filename):
    """Save data to a CSV file."""
    filepath = os.path.join(output_dir, filename)
    data.to_csv(filepath, index=False)
    logging.info(f"Saved {filepath}")


def sigmoid(x):
    """Sigmoid function."""
    return 1 / (1 + np.exp(-x))

# Read the data
repo_info = pd.read_csv('../data/sample_projects_total.csv')
# attritions = pd.read_csv('../result/attritions_updated_add_burst.csv')
productivity = pd.read_csv('../data/2025_0227_productivity.csv')
productivity["datetime"] = pd.to_datetime(productivity["datetime"])
global_min_time = productivity["datetime"].min()
productivity["standardized_time_weeks"] = (
  (productivity["datetime"] - global_min_time).dt.days // 7
).astype(int)

productivity

def compile_attrition_aggregated(attritions, near_days, burst_gap):

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - Line %(lineno)d - %(message)s',
        handlers=[logging.StreamHandler()]
    )

    df = attritions.copy()
    df['attrition_date'] = pd.to_datetime(df['attrition_date'])
    repo_names = df['repo_name'].unique()

    global_burst_id = 0
    aggregated_data = []

    for repo in repo_names:
        repo_df = df[df['repo_name'] == repo].copy().sort_values(by='attrition_date').reset_index(drop=True)
        burst_start = None
        burst_list = []
        for _, row in repo_df.iterrows():
            current_date = row['attrition_date']
            if burst_start is None or (current_date - burst_start).days > near_days:
                global_burst_id += 1
                burst_start = current_date
            burst_list.append(global_burst_id)
        repo_df['burst'] = burst_list
        aggregated_data.append(repo_df)

    attrition_aggregated = pd.concat(aggregated_data, ignore_index=True)
    attrition_aggregated['attrition_count'] = attrition_aggregated.groupby('burst')['burst'].transform('count')

    burst_details = attrition_aggregated.groupby(['repo_name', 'burst']).agg(
        start_date=('attrition_date', 'min'),
        end_date=('attrition_date', 'max')
    ).reset_index()
    burst_details.sort_values(['repo_name', 'start_date'], inplace=True)
    burst_details['previous_end_date'] = burst_details.groupby('repo_name')['end_date'].shift(1)
    burst_details['inter_burst_gap'] = (burst_details['start_date'] - burst_details['previous_end_date']).dt.days

    def flag_bursts(x):
        gap_mask = x['inter_burst_gap'].fillna(9999) < burst_gap
        next_gap = gap_mask.shift(-1, fill_value=False)
        x['gap_less_than_84'] = gap_mask | next_gap
        return x

    burst_details = burst_details.groupby('repo_name', group_keys=False).apply(flag_bursts)
    attrition_aggregated = attrition_aggregated.merge(
        burst_details[['repo_name', 'burst', 'gap_less_than_84', 'inter_burst_gap']],
        on=['repo_name', 'burst'],
        how='left'
    )

    return attrition_aggregated

attritions

# exclue null values in tenure
# 20250730 重要
attritions = attritions[attritions['tenure'].notnull()]
attritions['tenure'] = attritions['tenure'].astype(int)
attritions

attritions = compile_attrition_aggregated(attritions, near_days, burst_gap)
attritions = attritions[attritions['gap_less_than_84'] == False]
attritions

attritions.to_csv('../data/attritions_20250227_add_burst.csv', index=False)

a[a['attrition_count'] > 1]

a = pd.read_csv('../data/attritions_20250227_add_burst.csv')
a['attrition_count'].value_counts()

def merge_attrition_bursts(df):
  """
  Merge records within the same burst if there are multiple attrition events.
  For each group (same repo_name and burst):
    - If there are multiple records:
        - Set attrition_date as the latest (maximum) time.
        - Compute tenure as the average.
        - Sum commit_percent and commits.
    - Otherwise, keep the record unchanged.
  Returns a new DataFrame with the merged results.
  """
  merged_records = []
  grouped = df.groupby(['repo_name', 'burst'])
  for (repo, burst), group in grouped:
    if len(group) > 1:
      merged = group.iloc[0].copy()  # copy common info
      merged['attrition_date'] = group['attrition_date'].max()
      merged['tenure'] = group['tenure'].mean()
      merged['commit_percent'] = group['commit_percent'].sum()
      merged['commits'] = group['commits'].sum()
      merged_records.append(merged)
    else:
      merged_records.append(group.iloc[0])
  return pd.DataFrame(merged_records)

# Process attritions DataFrame and display the merged series data.
attritions = attritions.copy()  # if needed
attritions = merge_attrition_bursts(attritions)
attritions

attritions.reset_index(drop=True, inplace=True)


attritions

attritions.to_csv('../data/attritions_20250227_add_burst_merged.csv', index=False)

# def unify_repo_productivity_data(productivity_data):
#   '''fill blank values in uniformed timeweeks using vectorized operations'''
#   logging.info("Unifying productivity data...")
  
#   # Create all possible combinations of repos and time weeks
#   all_repos = productivity_data['repo_name'].unique()
#   time_range = range(
#     int(productivity_data['standardized_time_weeks'].min()),
#     int(productivity_data['standardized_time_weeks'].max()) + 1
#   )
  
#   # Create a cross join using pandas
#   repo_df = pd.DataFrame({'repo_name': all_repos})
#   time_df = pd.DataFrame({'standardized_time_weeks': time_range})
#   unified_frame = repo_df.assign(key=1).merge(time_df.assign(key=1), on='key').drop('key', axis=1)
  
#   # Merge with original data, filling missing values
#   unified_productivity_data = unified_frame.merge(
#     productivity_data,
#     on=['repo_name', 'standardized_time_weeks'],
#     how='left'
#   )
  
#   # Fill missing values with 0
#   unified_productivity_data['pr_throughput'] = unified_productivity_data['pr_throughput'].fillna(0)
  
#   # aggregate the data by standardized_time_weeks, summing pr_throughput, remaining columns are the same
#   unified_productivity_data = unified_productivity_data.groupby(['repo_name', 'standardized_time_weeks']).agg({
#     'pr_throughput': 'sum',
#   }).reset_index()
#   # 去除多余的rows，即repo的pr_throughput从>0开始的rows，到最后一个>0的rows
#   repo_first_time = unified_productivity_data[unified_productivity_data['pr_throughput'] > 0].groupby('repo_name').first().reset_index()
#   repo_last_time = unified_productivity_data[unified_productivity_data['pr_throughput'] > 0].groupby('repo_name').last().reset_index()
#   repo_time_range = repo_first_time.merge(repo_last_time, on='repo_name', suffixes=('_first', '_last'))
#   unified_productivity_data = unified_productivity_data.merge(repo_time_range, on='repo_name')
#   unified_productivity_data = unified_productivity_data[
#     (unified_productivity_data['standardized_time_weeks'] >= unified_productivity_data['standardized_time_weeks_first']) &
#     (unified_productivity_data['standardized_time_weeks'] <= unified_productivity_data['standardized_time_weeks_last'])
#   ]
  
#   # remove the intermediate columns
#   unified_productivity_data = unified_productivity_data.drop(columns=['standardized_time_weeks_first', 'standardized_time_weeks_last'])

  
#   return unified_productivity_data.reset_index(drop=True)

  
# # features for calculation

## 1. rolling slope trend
def calculate_rolling_slope_window(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling slope for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the slope for.
        output_column (str): Name of the output column for slopes.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling slope column.
    """
    
    def rolling_slope(df, column, window):
        y = df[column].values
        n = window
        if len(y) < n:
            return pd.Series([np.nan] * len(y), index=df.index)  # If not enough data, return NaN
        
        x = np.arange(1, n + 1)
        sum_x = np.sum(x)
        sum_x2 = np.sum(x ** 2)
        denominator = n * sum_x2 - sum_x ** 2
        
        sum_y = np.convolve(y, np.ones(n), 'valid')
        sum_xy = np.convolve(y, x[::-1], 'valid')
        
        slopes = (n * sum_xy - sum_x * sum_y) / denominator
        return pd.Series([np.nan] * (n - 1) + list(slopes), index=df.index)
    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling slope calculation for each group
    data[output_column] = data.groupby(group_column).apply(
        lambda group: rolling_slope(group, target_column, window_size)
    ).reset_index(level=0, drop=True)
    
    return data

## 2. rolling mean
def calculating_rolling_mean(data, group_column, target_column, output_column, window_size, sort_column):
    """
    Calculate the rolling mean for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the mean for.
        output_column (str): Name of the output column for means.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling mean column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling mean calculation for each group
    data[output_column] = data.groupby(group_column)[target_column].transform(
        lambda x: x.rolling(window=window_size, min_periods=window_size).mean()
    )
    
    return data

## 3. rolling rate of change
def calculate_rolling_rate_of_change(data, group_column, target_column, window_size, output_column, sort_column):
    """
    Calculate the rolling rate of change for a grouped column in a DataFrame.
    
    Parameters:
        data (pd.DataFrame): Input DataFrame.
        group_column (str): Column name to group by.
        target_column (str): Column name to calculate the rate of change for.
        output_column (str): Name of the output column for rate of change.
        window_size (int): Rolling window size.
        sort_column (str): Column name to sort within each group.
    
    Returns:
        pd.DataFrame: DataFrame with the added rolling rate of change column.
    """    
    # Sort data by group and specified sort column
    data = data.sort_values(by=[group_column, sort_column])
    
    # Apply rolling rate of change calculation for each group
    data['O_i_t_minus_1'] = data.groupby(group_column)[target_column].shift(1)
    data['I_it'] = np.log(
      (data[target_column] + 1) / (data['O_i_t_minus_1'] + 1)
    )
    data['sum_I_it_last_windows'] = data.groupby(group_column)['I_it'].transform(
      lambda x: x.rolling(window=window_size, min_periods=window_size).sum()
    )
    data[output_column] =data['sum_I_it_last_windows']
    
    # remove intermediate columns
    data = data.drop(columns=['O_i_t_minus_1', 'I_it', 'sum_I_it_last_windows'])
    
    return data
    


# 20250227 new 
def unify_repo_productivity_data(productivity_data):
    '''fill blank values in uniformed timeweeks using vectorized operations'''
    import logging
    logging.info("Unifying productivity data...")
    
    # 聚合数据，确保每个仓库和周的pr_throughput唯一
    productivity_data_agg = productivity_data.groupby(
        ['repo_name', 'standardized_time_weeks'], as_index=False
    )['pr_throughput'].sum()
    
    # 找出每个仓库有非零产出的周范围
    non_zero = productivity_data_agg['pr_throughput'] > 0
    if not non_zero.any():  # 处理全为0的情况
        return pd.DataFrame(columns=['repo_name', 'standardized_time_weeks', 'pr_throughput'])
    
    non_zero_data = productivity_data_agg[non_zero]
    repo_first_last = non_zero_data.groupby('repo_name')['standardized_time_weeks'].agg(
        first_week=('min'),
        last_week=('max')
    ).reset_index()
    
    # 生成每个仓库的有效周范围
    repo_first_last['weeks'] = repo_first_last.apply(
        lambda x: list(range(x['first_week'], x['last_week'] + 1)),
        axis=1
    )
    unified_frame = repo_first_last.explode('weeks')[['repo_name', 'weeks']]
    unified_frame.rename(columns={'weeks': 'standardized_time_weeks'}, inplace=True)
    
    # 合并数据并填充缺失值
    unified_productivity_data = unified_frame.merge(
        productivity_data_agg,
        on=['repo_name', 'standardized_time_weeks'],
        how='left'
    )
    unified_productivity_data['pr_throughput'] = unified_productivity_data['pr_throughput'].fillna(0)
    
    return unified_productivity_data.reset_index(drop=True)

p_test = unify_repo_productivity_data(productivity)
p_test


# calculate three features
window_size = 12
p_test = calculate_rolling_slope_window(p_test, 'repo_name', 'pr_throughput', 'rolling_slope', 12, 'standardized_time_weeks')
p_test = calculating_rolling_mean(p_test, 'repo_name', 'pr_throughput', 'rolling_mean', 12, 'standardized_time_weeks')
p_test = calculate_rolling_rate_of_change(p_test, 'repo_name', 'pr_throughput', 12, 'rolling_rate_of_change', 'standardized_time_weeks')
p_test

# using sigmod to merge two features: rolling_rate_of_change and rolling_mean
p_test['feature_sigmod_add'] = 1 / (1 + np.exp(-p_test['rolling_rate_of_change'] - p_test['rolling_mean']))
p_test['feature_sigmod_multiply'] = 1 / (1 + np.exp(-p_test['rolling_rate_of_change'] * p_test['rolling_mean']))
p_test

# Standardize the attrition dates to weeks
attritions['standardized_time_weeks'] = (
    (pd.to_datetime(attritions['attrition_date']) - global_min_time).dt.days // 7
).astype(int)

# Merge attrition data with p_test based on repo_name and standardized_time_weeks
attritions['someone_left'] = 1
p_test = p_test.merge(
    attritions[['repo_name', 'standardized_time_weeks', 'someone_left', 'tenure', 'commit_percent', 'commits', 'burst', 'attrition_count']],
    on=['repo_name', 'standardized_time_weeks'],
    how='left'
)

# Replace NaN in someone_left with 0
p_test['someone_left'] = p_test['someone_left'].fillna(0).astype(int)


p_test

p_test[p_test['someone_left'] == 1]

repo_info = pd.read_csv('../data/sample_projects_total.csv')
# merge with repo_info with columns 'mainLanguage', 'createdAt'
repo_info = repo_info[['name', 'mainLanguage', 'createdAt']]
# rename the column name to repo_name
repo_info = repo_info.rename(columns={'name': 'repo_name'})
# make createdAt to standardized_time_weeks
repo_info['createdAt'] = pd.to_datetime(repo_info['createdAt'])
repo_info['createdAt_standardized'] = (
    (repo_info['createdAt'] - global_min_time).dt.days // 7
).astype(int)
repo_info = repo_info.drop(columns=['createdAt'])
repo_info

# merge repo_info with p_test
p_test = p_test.merge(repo_info, on='repo_name')
p_test['duration'] = p_test['standardized_time_weeks'] - p_test['createdAt_standardized']
p_test

p_test.to_csv('../result/p_test.csv', index=False)

p_test = pd.read_csv('../result/p_test.csv')

p_test_burst = p_test[p_test['burst'] > 0]
p_test_burst

p_test_attrition = p_test.copy()
p_test_attrition = p_test_attrition[p_test_attrition['someone_left'] == 1]
p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'].notnull()]
p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'] != 0.5]
p_test_attrition

productivity_week = productivity.copy()
# for each repo_name within same standardized_time_weeks, just save the one with latest datetime
productivity_week = productivity_week.sort_values(by=['repo_name', 'datetime'], ascending=False)
productivity_week = productivity_week.drop_duplicates(subset=['repo_name', 'standardized_time_weeks'])
productivity_week = productivity_week.drop(columns=['datetime'])
productivity_week

p_test_attrition = p_test_attrition.merge(
    productivity_week[['repo_name', 'standardized_time_weeks', 'project_commits', 'project_contributors', 'project_age']],
    on= ['repo_name', 'standardized_time_weeks'],
)

p_test_attrition.to_csv(output_dir + "p_vary_time_attrition_20250227.csv", index=False)

p_test_attrition = pd.read_csv(output_dir + "p_vary_time_attrition_20250219.csv")

p_test_attrition['mainLanguage'].value_counts()

treatment_repos = set(p_test_attrition["repo_name"])
treatment_mask = (
    p_test["repo_name"].isin(treatment_repos)
) & (p_test["someone_left"] == 1)
treatment_features_df = p_test[treatment_mask].copy()
treatment_features_df

def compile_control_group_psm_knn(
    treatment_repos_with_left_date,
    candidate_repos,
    productivity_metric_data,
    n_neighbors,
    timewindow_weeks,
    feature_columns,
    extra_candidates=10,  # 为每个treatment多取一些candidate，以防时间窗口过滤导致配不上
):
    """
    使用 sklearn.neighbors.NearestNeighbors，避免一次性构造 NxM 距离矩阵。
    更新逻辑：对每个 treatment，只有在 treatment_time 的前后4的范围内的 control candidate 才参与匹配。
    """
    logging.info("Starting precision PSM with NearestNeighbors...")

    # 预处理控制组数据
    treatment_bursts = set(treatment_repos_with_left_date["burst"])
    treatment_repos = set(treatment_repos_with_left_date["repo_name"])
    available_controls = set(candidate_repos)
    logging.info(f"Available controls: {len(available_controls)} repositories to match {len(treatment_repos)} treatment repositories in {len(treatment_bursts)} bursts")

    # 构建带时间标识的控制组数据
    control_data = productivity_metric_data[
        productivity_metric_data["repo_name"].isin(available_controls)
    ].copy()
    control_data["time_key"] = control_data.apply(
        lambda x: (x["repo_name"], x["standardized_time_weeks"]), axis=1
    )

    # 预计算控制组离职时间点（按仓库分组排序）
    control_treatment_weeks = (
        productivity_metric_data[
            (productivity_metric_data["someone_left"] == 1) &
            (productivity_metric_data["repo_name"].isin(available_controls))
        ]
        .groupby("repo_name")["standardized_time_weeks"]
        .apply(sorted)
        .to_dict()
    )

    # 准备特征矩阵
    treatment_mask = (
        productivity_metric_data["repo_name"].isin(treatment_repos)
    ) & (productivity_metric_data["someone_left"] == 1)
    treatment_features_df = productivity_metric_data[treatment_mask].copy()

    X_treatment = treatment_features_df[feature_columns].values
    X_control = control_data[feature_columns].values

    # ---------------------------------------------------
    # 1) 构建 NearestNeighbors 索引
    # ---------------------------------------------------
    nn_model = NearestNeighbors(
        n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
        algorithm='auto'
    )
    nn_model.fit(X_control)

    matched_pairs = {}
    # 记录一遍, 让后面能通过索引反查控制组信息
    control_data_indexed = control_data.reset_index(drop=True)

    # ---------------------------------------------------
    # 2) 对每个 treatment，局部查询控制组
    # ---------------------------------------------------
    for i, (_, t_row) in enumerate(treatment_features_df.iterrows()):
        t_burst = t_row["burst"]
        t_repo = t_row["repo_name"]
        t_time = t_row["standardized_time_weeks"]
        used_time_keys = set()
        matched_controls = []

        # 根据需要反复扩大 neighbors 数量来查询
        neighbor_query_size = n_neighbors * extra_candidates

        while len(matched_controls) < n_neighbors and neighbor_query_size <= len(X_control):
            distances, indices = nn_model.kneighbors([X_treatment[i]], n_neighbors=neighbor_query_size, return_distance=True)
            indices = indices[0]
            distances = distances[0]

            candidate_indices = []
            for idx in indices:
                time_key = control_data_indexed.iloc[idx]["time_key"]
                if time_key not in used_time_keys:
                    candidate_indices.append(idx)

            for idx in candidate_indices:
                if len(matched_controls) >= n_neighbors:
                    break

                control_time_key = control_data_indexed.iloc[idx]["time_key"]
                control_repo, control_time = control_time_key

                # 限制只匹配在t_time前后4范围内的control
                if not (t_time - 4 <= control_time <= t_time + 4):
                    continue

                # 检查该repo是否已匹配，避免重复
                if control_repo in [c["repo_name"] for c in matched_controls]:
                    continue

                # 时间窗口过滤：验证控制组的离职事件情况
                treatment_weeks = control_treatment_weeks.get(control_repo, [])
                if treatment_weeks:
                    window_start = control_time - timewindow_weeks
                    window_end = control_time + timewindow_weeks
                    import bisect
                    left = bisect.bisect_left(treatment_weeks, window_start)
                    right = bisect.bisect_right(treatment_weeks, window_end)
                    if left < right:
                        continue

                matched_controls.append({
                    "repo_name": control_repo,
                    "matched_time": control_time,
                    "features": control_data_indexed.iloc[idx][feature_columns].values,
                })
                used_time_keys.add(control_time_key)

            neighbor_query_size *= 2
            if neighbor_query_size > len(X_control):
                break

        if matched_controls:
            matched_pairs[t_burst] = {
                "burst": t_burst,
                "repo_name": t_repo,
                "treatment_time": t_time,
                "controls": matched_controls,
                "treatment_features": t_row[feature_columns].values,
                "control_features": np.array([c["features"] for c in matched_controls]),
            }
            logging.info(f"Matched burst:{t_burst}-repo:{t_repo} with {len(matched_controls)} controls")
        else:
            logging.warning(f"No valid controls for {t_repo}")

    logging.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")
    return matched_pairs, treatment_features_df, control_data_indexed


p_test = p_test.fillna(0)
p_test_attrition = p_test_attrition.fillna(0)

# 转换类型
if 'burst' in p_test_attrition.columns:
    p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)

# 调用 PSM 函数进行匹配
matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
    p_test_attrition,
    p_test['repo_name'].tolist(),
    p_test,
    n_neighbors=5,
    timewindow_weeks=12,
    feature_columns=['feature_sigmod_add']
)


# import logging
# import numpy as np
# from sklearn.neighbors import NearestNeighbors
# import gc
# from typing import Dict, List, Tuple, Set, Any
# import pandas as pd

# def process_in_batches(
#     data: pd.DataFrame,
#     batch_size: int = 1000
# ) -> pd.DataFrame:
#     """按批次处理数据，避免一次性加载全部数据"""
#     for i in range(0, len(data), batch_size):
#         yield data.iloc[i:i + batch_size]

# def compile_control_group_psm_knn(
#     treatment_repos_with_left_date: pd.DataFrame,
#     candidate_repos: List[str],
#     productivity_metric_data: pd.DataFrame,
#     n_neighbors: int,
#     timewindow_weeks: int,
#     feature_columns: List[str],
#     extra_candidates: int = 10,
#     batch_size: int = 1000  # 添加批处理大小参数
# ) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
#     """
#     优化后的PSM-KNN匹配函数，使用批处理和内存管理
#     """
#     logging.info("Starting precision PSM with NearestNeighbors...")

#     # 预处理控制组数据
#     treatment_bursts = set(treatment_repos_with_left_date["burst"])
#     treatment_repos = set(treatment_repos_with_left_date["repo_name"])
#     available_controls = set(candidate_repos)
    
#     logging.info(f"Available controls: {len(available_controls)} repositories to match {len(treatment_repos)} treatment repositories in {len(treatment_bursts)} bursts")

#     # 构建带时间标识的控制组数据
#     control_data = productivity_metric_data[
#         productivity_metric_data["repo_name"].isin(available_controls)
#     ].copy()
    
#     # 使用更高效的方式创建time_key
#     control_data["time_key"] = list(zip(
#         control_data["repo_name"],
#         control_data["standardized_time_weeks"]
#     ))

#     # 预计算控制组离职时间点
#     control_treatment_weeks = {}
#     for name, group in productivity_metric_data[
#         (productivity_metric_data["someone_left"] == 1) &
#         (productivity_metric_data["repo_name"].isin(available_controls))
#     ].groupby("repo_name")["standardized_time_weeks"]:
#         control_treatment_weeks[name] = sorted(group.values)
    
#     # 准备特征矩阵
#     treatment_mask = (
#         productivity_metric_data["repo_name"].isin(treatment_repos)
#     ) & (productivity_metric_data["someone_left"] == 1)
#     treatment_features_df = productivity_metric_data[treatment_mask].copy()

#     X_treatment = treatment_features_df[feature_columns].values
#     X_control = control_data[feature_columns].values

#     # 构建NearestNeighbors索引
#     nn_model = NearestNeighbors(
#         n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
#         algorithm='auto'
#     )
#     nn_model.fit(X_control)

#     matched_pairs = {}
#     control_data_indexed = control_data.reset_index(drop=True)

#     # 批量处理treatment cases
#     for batch_df in process_in_batches(treatment_features_df, batch_size):
#         for i, (_, t_row) in enumerate(batch_df.iterrows()):
#             t_burst = t_row["burst"]
#             t_repo = t_row["repo_name"]
#             t_time = t_row["standardized_time_weeks"]
#             used_time_keys = set()
#             matched_controls = []

#             neighbor_query_size = n_neighbors * extra_candidates
#             t_features = t_row[feature_columns].values.reshape(1, -1)

#             while len(matched_controls) < n_neighbors and neighbor_query_size <= len(X_control):
#                 distances, indices = nn_model.kneighbors(
#                     t_features,
#                     n_neighbors=neighbor_query_size,
#                     return_distance=True
#                 )
#                 indices = indices[0]
#                 distances = distances[0]

#                 # 使用列表推导式优化
#                 candidate_indices = [
#                     idx for idx in indices
#                     if control_data_indexed.iloc[idx]["time_key"] not in used_time_keys
#                 ]

#                 for idx in candidate_indices:
#                     if len(matched_controls) >= n_neighbors:
#                         break

#                     control_row = control_data_indexed.iloc[idx]
#                     control_repo, control_time = control_row["time_key"]

#                     # 时间窗口过滤
#                     if not (t_time - 4 <= control_time <= t_time + 4):
#                         continue

#                     if control_repo in [c["repo_name"] for c in matched_controls]:
#                         continue

#                     # 验证控制组的离职事件
#                     treatment_weeks = control_treatment_weeks.get(control_repo, [])
#                     if treatment_weeks:
#                         window_start = control_time - timewindow_weeks
#                         window_end = control_time + timewindow_weeks
                        
#                         import bisect
#                         left = bisect.bisect_left(treatment_weeks, window_start)
#                         right = bisect.bisect_right(treatment_weeks, window_end)
#                         if left < right:
#                             continue

#                     matched_controls.append({
#                         "repo_name": control_repo,
#                         "matched_time": control_time,
#                         "features": control_row[feature_columns].values,
#                     })
#                     used_time_keys.add(control_row["time_key"])

#                 neighbor_query_size *= 2

#             if matched_controls:
#                 matched_pairs[t_burst] = {
#                     "burst": t_burst,
#                     "repo_name": t_repo,
#                     "treatment_time": t_time,
#                     "controls": matched_controls,
#                     "treatment_features": t_row[feature_columns].values,
#                     "control_features": np.array([c["features"] for c in matched_controls]),
#                 }
#                 logging.info(f"Matched burst:{t_burst}-repo:{t_repo} with {len(matched_controls)} controls")
#             else:
#                 logging.warning(f"No valid controls for {t_repo}")

#         # 批次处理完成后进行垃圾回收
#         gc.collect()

#     logging.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")
    
#     # 清理中间变量
#     del control_treatment_weeks
#     gc.collect()

#     return matched_pairs, treatment_features_df, control_data_indexed

# def prepare_data(
#     p_test: pd.DataFrame,
#     p_test_attrition: pd.DataFrame
# ) -> Tuple[pd.DataFrame, pd.DataFrame]:
#     """
#     预处理数据的函数
#     """
#     # 使用inplace=True来减少内存使用
#     p_test.fillna(0, inplace=True)
#     p_test_attrition.fillna(0, inplace=True)
    
#     # 转换数据类型以节省内存
#     p_test_attrition['burst'] = p_test_attrition['burst'].astype(np.int32)
    
#     return p_test, p_test_attrition

# p_test, p_test_attrition = prepare_data(p_test, p_test_attrition)

matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(  
    p_test_attrition,
    p_test['repo_name'].tolist(),
    p_test,
    5,
    12,
    ['feature_sigmod_add'],
    # batch_size=5000  # 设置适当的批处理大小
)

p_test

compiled_data = []
window_size = 12
cohort_id = 0
for treatment_repo, matched_data in matched_pairs.items():
    burst_id = matched_data["burst"]
    repo_name = matched_data["repo_name"]
    treatment_time = matched_data["treatment_time"]
    control_groups = matched_data["controls"]
    # load treatment and control data
    treatment_productivity = p_test[p_test['repo_name'] == repo_name].copy()
    control_productivity = p_test[p_test['repo_name'].isin([c['repo_name'] for c in control_groups])].copy()
    
    # only keep the rows within the time window for both treatment and control groups
    treatment_productivity = treatment_productivity[
        (treatment_productivity['standardized_time_weeks'] >= treatment_time - window_size) &
        (treatment_productivity['standardized_time_weeks'] <= treatment_time + window_size)
    ]
    # add ‘relativized_time’, ‘is_treated’, and ‘post_treatment’ columns
    treatment_productivity['relativized_time'] = treatment_productivity['standardized_time_weeks'] - treatment_time
    treatment_productivity['is_treated'] = 1
    treatment_productivity['post_treatment'] = treatment_productivity['relativized_time'] > 0
    
    # add ‘cohort_id’ to all dataframes
    treatment_productivity['cohort_id'] = cohort_id
    
    # add data to compiled_data within the time window
    compiled_data.append(treatment_productivity)
    control_dfs = []
    for c in control_groups:
        control_productivity = control_productivity[
            (control_productivity['repo_name'] == c['repo_name']) &
            (control_productivity['standardized_time_weeks'] >= c['matched_time'] - window_size) &
            (control_productivity['standardized_time_weeks'] <= c['matched_time'] + window_size)
        ]
        control_productivity['relativized_time'] = control_productivity['standardized_time_weeks'] - c['matched_time']
        control_productivity['is_treated'] = 0
        control_productivity['post_treatment'] = control_productivity['relativized_time'] > 0
        control_dfs.append(control_productivity)
    # add 'cohort_id' to all dataframes
    for df in control_dfs:
        df['cohort_id'] = cohort_id
    # add control data to compiled_data
    compiled_data.extend(control_dfs)  
    cohort_id += 1      
compiled_data = pd.concat(compiled_data)
compiled_data

compiled_data_test = compiled_data.copy()
compiled_data_test['is_post_treatment'] = compiled_data_test['post_treatment'].astype(int)
compiled_data_test['is_treated'] = compiled_data_test['is_treated'].astype(int)
compiled_data_test['is_treated_post_treatment'] = compiled_data_test['is_treated'] * compiled_data_test['post_treatment']

compiled_data_test = compiled_data_test.merge(
    productivity_week[['repo_name', 'standardized_time_weeks', 'project_commits', 'project_contributors', 'project_age']],
    on=['repo_name', 'standardized_time_weeks']
)

compiled_data_test

compiled_data_test.to_csv(output_dir + "compiled_data_test.csv", index=False)

compiled_data_test = pd.read_csv(output_dir + "compiled_data_test.csv")
compiled_data_test['log_pr_throughput'] = np.log(compiled_data_test['pr_throughput'] + 1)
compiled_data_test['log_project_commits'] = np.log(compiled_data_test['project_commits'] + 1)
compiled_data_test['log_project_contributors'] = np.log(compiled_data_test['project_contributors'] + 1)
compiled_data_test['log_project_age'] = np.log(compiled_data_test['project_age'] + 1)

# Create time-cohort and repo-cohort effects
compiled_data_test['time_cohort_effect'] = compiled_data_test['is_post_treatment'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)
compiled_data_test['repo_cohort_effect'] = compiled_data_test['is_treated'].astype(str) + '_' + compiled_data_test['cohort_id'].astype(str)

compiled_data_test = pd.read_csv(output_dir + "compiled_data_test.csv")

def visualize_trends(
    final_data, metric_column="pr_throughput", timewindow_weeks=None, n_neighbors=None
):
    """Visualize trends for treatment and control groups."""
    treatment_data = final_data[final_data["is_treated"] == 1]
    control_data = final_data[final_data["is_treated"] == 0]

    def compute_stats(group_data):
        grouped = group_data.groupby("relative_time_weeks")
        mean = grouped[metric_column].mean()
        std = grouped[metric_column].std()
        count = grouped[metric_column].count()
        sem = std / np.sqrt(count)
        return mean, mean - sem, mean + sem

    treatment_mean, treatment_lower, treatment_upper = compute_stats(treatment_data)
    control_mean, control_lower, control_upper = compute_stats(control_data)

    plt.figure(figsize=(12, 6))
    plt.plot(treatment_mean.index, treatment_mean, label="Treatment Mean", color="blue")
    plt.fill_between(
        treatment_mean.index,
        treatment_lower,
        treatment_upper,
        color="blue",
        alpha=0.2,
        label="Treatment 95% CI",
    )
    plt.plot(control_mean.index, control_mean, label="Control Mean", color="green")
    plt.fill_between(
        control_mean.index,
        control_lower,
        control_upper,
        color="green",
        alpha=0.2,
        label="Control 95% CI",
    )
    plt.axvline(0, color="red", linestyle="--", label="Treatment Start (time=0)")
    plt.title(f"{metric_column.capitalize()} Over Time (Treatment vs. Control)")
    plt.xlabel("Standardized Time (Weeks)")
    plt.ylabel(metric_column.capitalize())
    plt.legend()

    plot_filename = (
        f"{metric_column}_trend_plot_tw{timewindow_weeks}_nn{n_neighbors}.png"
    )
    plot_path = os.path.join(output_dir, plot_filename)
    plt.savefig(plot_path)
    logging.info(f"Saved plot to {plot_path}.")
    plt.show()


visualize_trends(compiled_data_test, metric_column="pr_throughput", timewindow_weeks=12, n_neighbors=5)



import numpy as np
import pandas as pd
# compiled_data_test = pd.read_csv('../result/did_result_20250408/compiled_data_test_12_pull_request_success_rate.csv')
compiled_data_test = pd.read_csv('../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers_new_added_outcomes_20250409.csv')

compiled_data_test
import matplotlib.pyplot as plt
# visualize the data, by relavitized_time, group by is_treated, calculate the mean of pr_throughput, in the same plot, add the standard deviation, add legend, add title, show deviation with error bar, show the mean difference between treatment and control

fig, ax = plt.subplots(figsize=(10, 6))


compiled_data_test.groupby(['relativized_time', 'is_treated'])['pr_throughput'].mean().unstack().plot(ax=ax)
plt.title("Mean PR Throughput by Relativized Time")
plt.xlabel("Relativized Time")
plt.ylabel("Mean PR Throughput")
plt.show()


# calculate the mean difference between treatment and control
mean_diff = compiled_data_test.groupby(['relativized_time', 'is_treated'])['pr_throughput'].mean().unstack().diff(axis=1).iloc[:, 1]
mean_diff

# calculate the standard deviation
std_diff = compiled_data_test.groupby(['relativized_time', 'is_treated'])['pr_throughput'].std().unstack().diff(axis=1).iloc[:, 1]
std_diff


# First, compute the mean, standard deviation, and count for each group by relativized_time.
# Then use these to calculate the standard error (sem) and 95% confidence intervals.
grouped = compiled_data_test.groupby(['relativized_time', 'is_treated'])['pr_throughput']
stats = grouped.agg(['mean', 'std', 'count']).reset_index()
stats['sem'] = stats['std'] / np.sqrt(stats['count'])
stats['ci_lower'] = stats['mean'] - 1.96 * stats['sem']
stats['ci_upper'] = stats['mean'] + 1.96 * stats['sem']

# Separate statistics for treatment (is_treated==1) and control (is_treated==0)
treatment_stats = stats[stats['is_treated'] == 1]
control_stats = stats[stats['is_treated'] == 0]

# Prepare a figure
fig, ax = plt.subplots(figsize=(12, 7))

# Plot the treatment group's mean with 95% confidence interval fill
ax.plot(treatment_stats['relativized_time'], treatment_stats['mean'], label='Treatment', 
  color='blue', marker='o')
ax.fill_between(treatment_stats['relativized_time'], treatment_stats['ci_lower'], 
    treatment_stats['ci_upper'], color='blue', alpha=0.3)

# Plot the control group's mean with 95% confidence interval fill
ax.plot(control_stats['relativized_time'], control_stats['mean'], label='Control', 
  color='green', marker='o')
ax.fill_between(control_stats['relativized_time'], control_stats['ci_lower'], 
    control_stats['ci_upper'], color='green', alpha=0.3)

# Add a vertical dashed red line at the treatment time point (relativized_time == 0)
ax.axvline(0, color='red', linestyle='--', label='Treatment Start')

# (Optional) Calculate and plot the mean difference (treatment - control)
# Merge the treatment and control statistics based on the relativized_time.
diff_data = pd.merge(
    treatment_stats[['relativized_time', 'mean']],
    control_stats[['relativized_time', 'mean']],
    on='relativized_time',
    suffixes=('_treat', '_control')
)
diff_data['mean_diff'] = diff_data['mean_treat'] - diff_data['mean_control']

# Plot the mean difference on the same figure (using a dotted purple line)
# ax.plot(diff_data['relativized_time'], diff_data['mean_diff'], label='Mean Difference (T-C)', 
  # color='purple', linestyle=':', marker='o')

# Customize labels, title, and legend
ax.set_title("PR Throughput Trends: Treatment vs. Control with 95% CI")
ax.set_xlabel("Relativized Time (Weeks)")
ax.set_ylabel("Mean PR Throughput")
ax.legend()
plt.tight_layout()
plt.show()

metrics = ['pr_throughput', 'pull_request_success_rate', 'time_to_merge']
# compiled_data_test = pd.read_csv('../result/did_result_20250312/compiled_data_test_12_time_to_merge.csv')

# compiled_data_test = pd.read_csv('../result/did_result_20250408/compiled_data_test_12_time_to_merge.csv')
# compiled_data_test = pd.read_csv('../result/did_result_20250408/compiled_data_test_12_pull_request_success_rate.csv')
compiled_data_test = pd.read_csv('../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers_new_added_outcomes_20250409.csv')

for metric in metrics:
    fig, ax = plt.subplots(figsize=(10, 6))


    compiled_data_test.groupby(['relativized_time', 'is_treated'])[metric].mean().unstack().plot(ax=ax)
    plt.title(f"Mean {metric} by Relativized Time")
    plt.xlabel("Relativized Time")
    plt.ylabel(f"Mean {metric}")
    plt.show()


    # calculate the mean difference between treatment and control
    mean_diff = compiled_data_test.groupby(['relativized_time', 'is_treated'])[metric].mean().unstack().diff(axis=1).iloc[:, 1]
    mean_diff

    # calculate the standard deviation
    std_diff = compiled_data_test.groupby(['relativized_time', 'is_treated'])[metric].std().unstack().diff(axis=1).iloc[:, 1]
    std_diff


    # First, compute the mean, standard deviation, and count for each group by relativized_time.
    # Then use these to calculate the standard error (sem) and 95% confidence intervals.
    grouped = compiled_data_test.groupby(['relativized_time', 'is_treated'])[metric]
    stats = grouped.agg(['mean', 'std', 'count']).reset_index()
    stats['sem'] = stats['std'] / np.sqrt(stats['count'])
    stats['ci_lower'] = stats['mean'] - 1.96 * stats['sem']
    stats['ci_upper'] = stats['mean'] + 1.96 * stats['sem']

    # Separate statistics for treatment (is_treated==1) and control (is_treated==0)
    treatment_stats = stats[stats['is_treated'] == 1]
    control_stats = stats[stats['is_treated'] == 0]

    # Prepare a figure
    fig, ax = plt.subplots(figsize=(12, 7))

    # Plot the treatment group's mean with 95% confidence interval fill
    ax.plot(treatment_stats['relativized_time'], treatment_stats['mean'], label='Treatment', 
    color='blue', marker='o')
    ax.fill_between(treatment_stats['relativized_time'], treatment_stats['ci_lower'], 
        treatment_stats['ci_upper'], color='blue', alpha=0.3)

    # Plot the control group's mean with 95% confidence interval fill
    ax.plot(control_stats['relativized_time'], control_stats['mean'], label='Control', 
    color='green', marker='o')
    ax.fill_between(control_stats['relativized_time'], control_stats['ci_lower'], 
        control_stats['ci_upper'], color='green', alpha=0.3)

    # Add a vertical dashed red line at the treatment time point (relativized_time == 0)
    ax.axvline(0, color='red', linestyle='--', label='Treatment Start')

    # (Optional) Calculate and plot the mean difference (treatment - control)
    # Merge the treatment and control statistics based on the relativized_time.
    diff_data = pd.merge(
        treatment_stats[['relativized_time', 'mean']],
        control_stats[['relativized_time', 'mean']],
        on='relativized_time',
        suffixes=('_treat', '_control')
    )
    diff_data['mean_diff'] = diff_data['mean_treat'] - diff_data['mean_control']

    # Plot the mean difference on the same figure (using a dotted purple line)
    # ax.plot(diff_data['relativized_time'], diff_data['mean_diff'], label='Mean Difference (T-C)', 
    # color='purple', linestyle=':', marker='o')

    # Customize labels, title, and legend
    ax.set_title(f"{metric} Trends: Treatment vs. Control with 95% CI")
    ax.set_xlabel("Relativized Time (Weeks)")
    ax.set_ylabel(f"Mean {metric}")
    ax.legend()
    plt.tight_layout()
    plt.show()

import seaborn as sns
import matplotlib.pyplot as plt

metrics = ['pr_throughput', 'pull_request_success_rate', 'time_to_merge']
compiled_data_test = pd.read_csv('../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers_new_added_outcomes_20250409.csv')

sns.set(font_scale=2)  # 设置字体大小
plt.rcParams['figure.figsize'] = (10, 7)  # 设置图形比例

for metric in metrics:
    # First, compute the mean, standard deviation, and count for each group by relativized_time.
    # Then use these to calculate the standard error (sem) and 95% confidence intervals.
    grouped = compiled_data_test.groupby(['relativized_time', 'is_treated'])[metric]
    stats = grouped.agg(['mean', 'std', 'count']).reset_index()
    stats['sem'] = stats['std'] / np.sqrt(stats['count'])
    stats['ci_lower'] = stats['mean'] - 1.96 * stats['sem']
    stats['ci_upper'] = stats['mean'] + 1.96 * stats['sem']

    # Separate statistics for treatment (is_treated==1) and control (is_treated==0)
    treatment_stats = stats[stats['is_treated'] == 1]
    control_stats = stats[stats['is_treated'] == 0]

    # Prepare a figure
    fig, ax = plt.subplots()

    # Plot the treatment group's mean with 95% confidence interval fill
    ax.plot(treatment_stats['relativized_time'], treatment_stats['mean'], label='Treatment', 
            color='blue', marker='o')
    ax.fill_between(treatment_stats['relativized_time'], treatment_stats['ci_lower'], 
                    treatment_stats['ci_upper'], color='blue', alpha=0.3)

    # Plot the control group's mean with 95% confidence interval fill
    ax.plot(control_stats['relativized_time'], control_stats['mean'], label='Control', 
            color='green', marker='o')
    ax.fill_between(control_stats['relativized_time'], control_stats['ci_lower'], 
                    control_stats['ci_upper'], color='green', alpha=0.3)

    # Add a vertical dashed red line at the treatment time point (relativized_time == 0)
    ax.axvline(0, color='red', linestyle='--', label='Treatment Happened')

    # Customize labels, title, and legend
    # 添加metric映射的标题
    metric_titles = {
                'pr_throughput': 'PR Throughput',
                'pull_request_success_rate': 'Pull Request Accept Rate',
                'time_to_merge': 'Time to Merge'
        }
#     ax.set_title(f"{metric_titles[metric]}")
    ax.set_xlabel("Relativized Week", fontsize=26)
#     ax.set_ylabel(f"Mean {metric}")
    # 去除水平方向左右多出来的空白位置
    ax.set_xlim(-13, 13)
    ax.legend()
    plt.tight_layout()
    # savefig
    plt.savefig(f"../figures/{metric}_parallel_trends.pdf", dpi=300, bbox_inches='tight')
    plt.show()


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import eventstudy as es

# Load data
compiled_data_test = pd.read_csv("../result/did_result_20250212/compiled_data_test.csv")

# Ensure relativized_time is an integer
compiled_data_test['relativized_time'] = compiled_data_test['relativized_time'].astype(int)

# Define the event study model
event_study = EventStudy(
    data=compiled_data_test,
    event_date_column='relativized_time',
    outcome_column='log_pr_throughput',
    entity_column='cohort_id',
    event_window=(-12, 12),  # Pre and post periods
    estimation_window=(-24, -13)  # Estimation window
)




# Run the event study without control variables
results_without_controls = event_study.run_event_study()

# Run the event study with control variables
results_with_controls = event_study.run_event_study(
    control_columns=['log_project_commits', 'log_project_contributors', 'log_project_age']
)